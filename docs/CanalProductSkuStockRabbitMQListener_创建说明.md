# CanalProductSkuStockRabbitMQListener 创建说明

## 概述
模仿 `CanalOrderRabbitMQListener` 创建了 `CanalProductSkuStockRabbitMQListener` 类，用于处理产品库存的Canal变更消息。

## 创建的文件

### 1. MQ常量配置
**文件**: `hengjian-extend/hengjian-stream-mq/src/main/java/com/hengjian/stream/mq/constant/RabbitMqConstant.java`

添加了以下常量：
```java
/**
 * canal产品库存交换机
 */
public static final String CANAL_PRODUCT_SKU_STOCK_EXCHANGE = "canal_product_sku_stock_exchange";

/**
 * canal产品库存Key
 */
public static final String CANAL_PRODUCT_SKU_STOCK_KEY = "canal_product_sku_stock_key";

/**
 * canal产品库存队列
 */
public static final String CANAL_PRODUCT_SKU_STOCK_QUEUE = "canal_product_sku_stock_queue";
```

### 2. MQ配置
**文件**: `hengjian-extend/hengjian-stream-mq/src/main/java/com/hengjian/stream/mq/config/RabbitMqConfig.java`

添加了以下Bean配置：
- `canalProductSkuStockExchange()` - 交换机
- `canalProductSkuStockQueue()` - 队列（支持单一活动消费者模式）
- `canalProductSkuStockQueueBinding()` - 绑定关系

### 3. MQ消息DTO
**文件**: `hengjian-business/zsmall-product/zsmall-product-entity/src/main/java/com/zsmall/product/entity/domain/mq/CanalProductSkuStockMqDTO.java`

Canal产品库存MQ消息DTO，包含：
- `database` - 数据库名
- `table` - 表名
- `type` - 操作类型 (INSERT, UPDATE, DELETE)
- `data` - 最新的数据
- `old` - 变更前的数据

### 4. Canal产品库存数据实体
**文件**: `hengjian-business/zsmall-product/zsmall-product-entity/src/main/java/com/zsmall/product/entity/domain/mq/CanalProductSkuStock.java`

Canal产品库存数据实体，包含产品库存表的主要字段：
- 基本信息：id, stockCode, productSkuCode, warehouseSystemCode
- 库存信息：stockTotal, stockReserved, stockAvailable, dropShippingStockAvailable
- 锁定信息：pickupLockUsed, pickupLockReserved, dropShippingLockUsed, dropShippingLockReserved
- 其他信息：stockState, lockExceptionCode, logisticsTemplateNo, tenantId等

### 5. Canal产品库存处理支持类
**文件**: `hengjian-business/zsmall-product/zsmall-product-biz/src/main/java/com/zsmall/product/biz/support/CanalProductSkuStockSupport.java`

处理Canal产品库存变更的核心业务逻辑类，包含：
- `dealCanalProductSkuStockMqMessage()` - 主要处理方法
- `dealCanalProductSkuStockMqMessageData()` - 数据处理逻辑
- `processInsertFields()` - 处理插入操作（TODO）
- `processUpdateFields()` - 处理更新操作（TODO）
- `processDeleteFields()` - 处理删除操作（TODO）

### 6. Canal产品库存RabbitMQ监听器
**文件**: `hengjian-business/zsmall-product/zsmall-product-biz/src/main/java/com/zsmall/product/biz/mq/CanalProductSkuStockRabbitMQListener.java`

主要的MQ监听器类，特点：
- 使用 `@ConditionalOnProperty(name = "xxl.job.enabled", havingValue = "false")` 条件注解
- 监听 `CANAL_PRODUCT_SKU_STOCK_QUEUE` 队列
- 并发度设置为1，使用 `canalListenerContainerFactory`
- 异常处理：使用现有的 `order_es_processing_failed` 表记录失败消息
- 消息确认：处理成功或失败都会进行ACK确认

## 设计特点

1. **完全模仿订单Canal监听器的结构**：保持代码风格和处理逻辑的一致性
2. **复用现有异常处理机制**：使用 `CanalProcessingFailedMapper` 记录处理失败的消息
3. **支持分布式锁**：使用Redisson实现消息处理的分布式锁
4. **防重复处理**：通过Redis缓存消息Hash值防止重复处理
5. **预留扩展接口**：在Support类中预留了具体业务逻辑的实现接口

## 使用说明

1. **具体业务逻辑实现**：需要在 `CanalProductSkuStockSupport` 类的三个处理方法中实现具体的业务逻辑
2. **队列配置**：确保RabbitMQ中已创建对应的交换机和队列
3. **Canal配置**：需要配置Canal监听 `product_sku_stock` 表的变更并发送到对应的MQ队列

## 注意事项

1. 监听器只在 `xxl.job.enabled=false` 时生效
2. 使用单一活动消费者模式，确保消息顺序处理
3. 异常消息会记录到 `canal_processing_failed` 表中
4. 具体的业务处理逻辑需要根据实际需求在Support类中实现

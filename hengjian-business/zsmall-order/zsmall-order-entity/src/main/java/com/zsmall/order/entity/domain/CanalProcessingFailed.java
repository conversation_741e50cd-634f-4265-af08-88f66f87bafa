package com.zsmall.order.entity.domain;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Canal消息处理失败对象 canal_processing_failed
 *
 * <AUTHOR>
 * @date 2025-03-24
 */
@Data
@TableName("canal_processing_failed")
public class CanalProcessingFailed implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 数据库名称
     */
    private String databaseName;

    /**
     * 表名
     */
    private String tableName;

    /**
     * 操作类型
     */
    private String type;

    /**
     * message对象
     */
    private String messageObject;

    /**
     * channel对象
     */
    private String channelObject;

    /**
     * 错误原因
     */
    private String errorMessage;

    /**
     * 异常处理次数
     */
    private Integer processingNum;

    /**
     * 异常处理状态 0处理失败  1处理成功
     */
    private Integer processingStatus;

    /**
     *
     */
    @TableLogic
    private Integer delFlag;
    /**
     * 更新时间
     */
    private Date updateTime;
    private Date createTime;


}

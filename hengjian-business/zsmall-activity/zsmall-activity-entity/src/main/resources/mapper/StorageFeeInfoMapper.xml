<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.activity.entity.mapper.StorageFeeInfoMapper">

    <resultMap id="pageOrderByJson" type="com.zsmall.activity.entity.domain.vo.storageFee.StorageFeeInfoVo">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="storage_fee_id" property="storageFeeId"/>
        <result column="fee_state" property="feeState"/>
        <result column="currency_code" property="currencyCode"/>
        <result column="currency_symbol" property="currencySymbol"/>
        <result column="total_storage_fee" property="totalStorageFee"/>
        <result column="storage_fee_settlement_date" property="storageFeeSettlementDate"/>
        <result column="activity_id" property="activityId"/>
        <result column="activity_type" property="activityType"/>
        <result column="logistics_type" property="logisticsType"/>
        <result column="pay_state" property="payState"/>
        <result column="pay_time" property="payTime"/>
        <result column="send_time" property="sendTime"/>
        <result column="error_message" property="errorMessage" jdbcType="VARCHAR" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
    </resultMap>

    <select id="queryPageListByJson" resultMap="pageOrderByJson">
        select
            id,
            tenant_id,
            storage_fee_id,
            fee_state,
            currency_code,
            currency_symbol,
            total_storage_fee,
            storage_fee_settlement_date,
            activity_id,
            activity_type,
            logistics_type,
            pay_state,
            pay_time,
            send_time,
            error_message
        from storage_fee_info
        <where>
            del_flag = 0
            <if test="bo.ids != null and bo.ids.size() > 0">
                and id in
                <foreach item="id" collection="bo.ids" separator="," open="(" close=")" index="">
                    #{id}
                </foreach>
            </if>
            <if test="bo.tenantId != null">
                and tenant_id like concat('%',#{bo.tenantId},'%')
            </if>
            <if test="bo.storageFeeId != null">
                and storage_fee_id like concat('%',#{bo.storageFeeId},'%')
            </if>
            <if test="bo.currencyCode != null">
                and currency_code = #{bo.currencyCode}
            </if>
            <if test="bo.payState != null">
                and pay_state = #{bo.payState}
            </if>
            <if test="bo.storageFeeSettlementDateStart != null and  bo.storageFeeSettlementDateStart != '' and bo.storageFeeSettlementDateEnd != null and  bo.storageFeeSettlementDateEnd != ''">
                and storage_fee_settlement_date between #{storageFeeSettlementDateStart} and #{storageFeeSettlementDateEnd}
            </if>
            <if test="bo.activityId != null">
                and activity_id like concat('%',#{bo.activityId},'%')
            </if>
            <if test="bo.activityType != null">
                and activity_type = #{bo.activityType}
            </if>
            <if test="bo.logisticsType != null">
                and logistics_type = #{bo.logisticsType}
            </if>
            <if test="bo.feeState != null">
                and fee_state = #{bo.feeState}
            </if>
            <if test="bo.feeStateList != null and bo.feeStateList.size() > 0">
                and fee_state in <foreach item="feeState" collection="bo.feeStateList" separator="," open="(" close=")" >
                    #{feeState}
                </foreach>
            </if>
            <if test="bo.payTimeStart != null and  bo.payTimeStart != '' and bo.payTimeEnd != null and  bo.payTimeEnd != ''">
                and pay_time between #{bo.payTimeStart} and #{bo.payTimeEnd}
            </if>
            <if test="bo.sendTimeStart != null and  bo.sendTimeStart != '' and bo.sendTimeEnd != null and  bo.sendTimeEnd != ''">
                and send_time between #{bo.sendTimeStart} and #{bo.sendTimeEnd}
            </if>
        </where>
    </select>


</mapper>

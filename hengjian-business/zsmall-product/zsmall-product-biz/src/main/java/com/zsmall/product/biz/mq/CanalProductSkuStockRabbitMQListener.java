package com.zsmall.product.biz.mq;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.hengjian.stream.mq.constant.RabbitMqConstant;
import com.rabbitmq.client.Channel;
import com.zsmall.order.entity.domain.CanalProcessingFailed;
import com.zsmall.order.entity.mapper.CanalProcessingFailedMapper;
import com.zsmall.product.biz.support.CanalProductSkuStockSupport;
import com.zsmall.product.entity.domain.mq.CanalProductSkuStockMqDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Arrays;
import java.util.Date;

/**
 * Canal产品库存RabbitMQ监听器
 *
 * <AUTHOR>
 */
@Slf4j
@ConditionalOnProperty(name = "xxl.job.enabled", havingValue = "false")
@Component
public class CanalProductSkuStockRabbitMQListener {

    @Resource
    private CanalProcessingFailedMapper canalProcessingFailedMapper;

    @Resource
    private CanalProductSkuStockSupport canalProductSkuStockSupport;

    @RabbitHandler
    @RabbitListener(queues = RabbitMqConstant.CANAL_PRODUCT_SKU_STOCK_QUEUE,
            concurrency = "1",
            containerFactory = "canalListenerContainerFactory")
    public void canalProductSkuStock(Message message, @Header(AmqpHeaders.CHANNEL) Channel channel) throws IOException {
        try {
            canalProductSkuStockSupport.dealCanalProductSkuStockMqMessage(message, channel, false);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            CanalProcessingFailed od = new CanalProcessingFailed();
            od.setChannelObject(JSON.toJSONString(channel));
            od.setMessageObject(JSON.toJSONString(message));
            od.setProcessingNum(0);
            od.setProcessingStatus(0);
            od.setCreateTime(new Date());
            od.setUpdateTime(new Date());

            try {
                String messageContext = new String(message.getBody());
                CanalProductSkuStockMqDTO canalProductSkuStockDTO = JSONUtil.toBean(messageContext, CanalProductSkuStockMqDTO.class);
                //记录进Canal处理失败表
                od.setId(IdUtil.getSnowflakeNextId());
                if (ObjectUtil.isNotNull(canalProductSkuStockDTO)) {
                    od.setDatabaseName(canalProductSkuStockDTO.getDatabase());
                    od.setTableName(canalProductSkuStockDTO.getTable());
                    od.setType(canalProductSkuStockDTO.getType());
                }
                log.error("处理产品库存Canal变更MQ消息失败,完整堆栈信息：", e);
                od.setErrorMessage(StrUtil.format("处理产品库存Canal变更MQ消息失败,原因：{}，堆栈信息：{}",
                        e.getCause() != null ? e.getCause().getMessage() : e.getMessage(),
                        Arrays.toString(e.getStackTrace())));
            } catch (Exception ex) {
                log.error("记录处理产品库存Canal变更异常信息失败，原始异常：{}，记录异常：{}，消息体：{}",
                        e.getMessage(),
                        ex.getMessage(),
                        message,
                        ex);
                od.setErrorMessage(StrUtil.format("记录处理产品库存Canal变更异常信息失败，原因：{}，堆栈信息：{}",
                        ex.getCause() != null ? ex.getCause().getMessage() : ex.getMessage(),
                        Arrays.toString(ex.getStackTrace())));
            } finally {
                canalProcessingFailedMapper.insert(od);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            }
        }
    }
}

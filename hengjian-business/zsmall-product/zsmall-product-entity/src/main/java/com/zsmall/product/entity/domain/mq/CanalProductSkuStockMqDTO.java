package com.zsmall.product.entity.domain.mq;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Canal产品库存MQ消息DTO
 *
 * <AUTHOR>
 */
@Data
public class CanalProductSkuStockMqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 数据库名
     */
    private String database;

    /**
     * 表名
     */
    private String table;

    /**
     * 操作类型 (INSERT, UPDATE, DELETE)
     */
    private String type;

    /**
     * 最新的数据
     */
    private List<CanalProductSkuStock> data;

    /**
     * 变更前的数据
     */
    private List<CanalProductSkuStock> old;
}
